<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <title>验证码组件</title>
        <!-- vConsole for development -->
        <style>
            body {
                position: relative;
                margin: 0;
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB",
                    "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
                background-color: #f5f5f5;
            }

            .captcha-container {
                background: white;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .captcha-title {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: 20px;
            }

            #captcha-element {
                width: 100%;
                min-height: 50px;
                margin-bottom: 20px;
            }

            #validate-button {
                width: 100%;
                height: 44px;
                background: #1890ff;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            #validate-button:hover {
                background: #40a9ff;
            }

            #validate-button:disabled {
                background: #d9d9d9;
                cursor: not-allowed;
            }

            .status-message {
                margin-top: 15px;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
                font-size: 14px;
                display: none;
            }

            .status-success {
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                color: #52c41a;
            }

            .status-error {
                background: #fff2f0;
                border: 1px solid #ffccc7;
                color: #ff4d4f;
            }

            .status-loading {
                background: #e6f7ff;
                border: 1px solid #91d5ff;
                color: #1890ff;
            }

            /* 全屏Loading样式 */
            .fullscreen-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.95);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
                transition: opacity 0.3s ease-in-out;
                opacity: 1;
            }

            .loading-spinner {
                width: 50px;
                height: 50px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #1890ff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 20px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .loading-text {
                font-size: 16px;
                color: #666;
                text-align: center;
                line-height: 1.5;
            }

            .loading-dots {
                display: inline-block;
                width: 20px;
                text-align: left;
            }

            .loading-dots::after {
                content: '';
                animation: dots 1.5s steps(4, end) infinite;
            }

            @keyframes dots {
                0%, 20% { content: ''; }
                40% { content: '.'; }
                60% { content: '..'; }
                80%, 100% { content: '...'; }
            }

            /* 隐藏状态 */
            .fullscreen-loading.hidden {
                opacity: 0;
                pointer-events: none;
            }
        </style>
    </head>

    <body>
        <!-- 全屏Loading -->
        <div id="fullscreen-loading" class="fullscreen-loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">
                <span class="loading-dots"></span>
            </div>
        </div>

        <div class="captcha-container" style="display: none;">
            <div class="captcha-title" id="captcha-title">安全验证</div>
            <div id="captcha-element"></div>
            <div id="button" class="login-btn"></div>
            <button id="validate-button">开始验证</button>
            <div id="status-message" class="status-message"></div>

            <!-- 调试信息 -->
            <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
                <div>调试信息:</div>
                <div id="debug-info">正在加载...</div>
                <div style="margin-top: 8px; color: #666; font-size: 11px">
                    💡 开发环境时会自动启用vConsole调试面板，可在右下角找到调试按钮
                </div>
                <button onclick="testCaptcha()" style="margin-top: 10px; padding: 5px 10px; font-size: 12px">
                    测试验证码弹窗
                </button>
            </div>
        </div>

        <script>
            // 全局变量
            var captcha = null;
            var isValidating = false;
            var currentLanguage = "cn"; // 默认中文
            var isDevelopment = false;

            // 状态管理
            var ValidationState = {
                IDLE: "idle",
                LOADING: "loading",
                SUCCESS: "success",
                ERROR: "error",
            };

            // 从地址栏获取参数配置
            function getUrlConfig() {
                var urlParams = new URLSearchParams(window.location.search);

                // 获取语言参数，支持 lang 和 language 两种参数名
                var langParam = urlParams.get("lang") || urlParams.get("language") || "CN";

                // 语言映射表：URL参数 -> 内部语言代码
                var languageMapping = {
                    CN: "cn",
                    EN: "en",
                    ES: "es",
                    PTBR: "ptbr",
                    PT: "ptbr", // 兼容简写
                    RU: "ru",
                };

                // 设置语言，默认为中文
                var detectedLanguage = languageMapping[langParam.toUpperCase()] || "cn";

                // 获取环境参数
                var envParam = urlParams.get("env") || urlParams.get("environment") || "production";
                var detectedEnv =
                    envParam.toLowerCase() === "development" ||
                    envParam.toLowerCase() === "dev" ||
                    envParam.toLowerCase() === "test";

                console.log("URL配置解析:", {
                    原始语言参数: langParam,
                    检测到的语言: detectedLanguage,
                    原始环境参数: envParam,
                    检测到的环境: detectedEnv ? "development" : "production",
                });

                return {
                    language: detectedLanguage,
                    isDevelopment: detectedEnv,
                };
            }

            // 显示状态消息
            function showStatus(state, message) {
                var statusElement = document.getElementById("status-message");
                var button = document.getElementById("validate-button");

                statusElement.className = "status-message status-" + state;
                statusElement.textContent = message;
                statusElement.style.display = "block";

                if (state === ValidationState.LOADING) {
                    button.disabled = true;
                    button.textContent = "Verifying...";
                } else {
                    button.disabled = false;
                    button.textContent = "Start Verification";
                }
            }

            // 隐藏状态消息
            function hideStatus() {
                document.getElementById("status-message").style.display = "none";
            }

            // 显示全屏Loading
            function showFullscreenLoading(text) {
                var loadingElement = document.getElementById("fullscreen-loading");
                // var textElement = loadingElement.querySelector(".loading-text");
                if (loadingElement) {
                    // if (text) {
                    //     textElement.innerHTML = text + '<span class="loading-dots"></span>';
                    // }
                    loadingElement.classList.remove("hidden");
                    loadingElement.style.display = "flex";
                }
            }

            // 隐藏全屏Loading
            function hideFullscreenLoading() {
                var loadingElement = document.getElementById("fullscreen-loading");
                if (loadingElement) {
                    loadingElement.classList.add("hidden");
                    setTimeout(function() {
                        loadingElement.style.display = "none";
                    }, 300); // 等待过渡动画完成后完全隐藏
                }
            }

            // 更新调试信息
            function updateDebugInfo() {
                var debugElement = document.getElementById("debug-info");
                if (debugElement) {
                    var urlParams = new URLSearchParams(window.location.search);
                    var config = getSceneConfig();

                    var info = [
                        "SDK加载: " + (window.initAliyunCaptcha ? "✓" : "✗"),
                        "验证码实例: " + (captcha ? "✓" : "✗"),
                        "正在验证: " + (isValidating ? "是" : "否"),
                        "当前语言: " +
                            currentLanguage +
                            " (URL: " +
                            (urlParams.get("lang") || urlParams.get("language") || "无") +
                            ")",
                        "环境: " +
                            (isDevelopment ? "开发" : "生产") +
                            " (URL: " +
                            (urlParams.get("env") || urlParams.get("environment") || "无") +
                            ")",
                        "vConsole: " + (isDevelopment ? (window.VConsole ? "✓ 已加载" : "⏳ 加载中") : "✗ 生产环境"),
                        "网络状态: " + (navigator.onLine ? "✅ 在线" : "❌ 离线"),
                        "设备类型: " + (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ? "📱 移动端" : "💻 PC端"),
                        "浏览器: " + getBrowserInfo().browserName,
                        "屏幕尺寸: " + screen.width + "x" + screen.height,
                        "SceneId: " + config.SceneId,
                        "时间: " + new Date().toLocaleTimeString(),
                    ];
                    debugElement.innerHTML = info.join("<br>");
                }
            }

            // 环境检测和配置获取
            function getSceneConfig() {
                // 生产环境配置
                var productionConfig = {
                    SceneId: "s4yebkic",
                    prefix: "153y4u",
                };

                // 开发/测试环境配置
                var developmentConfig = {
                    SceneId: "1e4xl9lr",
                    prefix: "153y4u",
                };

                return isDevelopment ? developmentConfig : productionConfig;
            }

            // 初始化验证码
            function initCaptcha() {
                return new Promise(function (resolve, reject) {
                    var initResolve = resolve;
                    var initReject = reject;

                    // 设置更长的超时时间（移动端网络考虑）
                    var timeout = setTimeout(function () {
                        console.error("❌ Captcha initialization timeout (考虑移动端网络延迟)");
                        if (initReject) {
                            initReject(new Error("Captcha initialization timeout - check network connection"));
                        }
                    }, 30000); // 从10秒增加到30秒

                    // 检查网络状态
                    if (!checkNetworkStatus()) {
                        clearTimeout(timeout);
                        reject(new Error("No network connection"));
                        return;
                    }

                    // 模仿Vue组件的SDK检查逻辑
                    if (!window.initAliyunCaptcha) {
                        console.error('下载无痕验证失败，重新下载');
                    }

                    // 首先确保SDK已加载
                    ensureSDKLoaded()
                        .then(function () {
                            try {
                                if (!window.initAliyunCaptcha) {
                                    clearTimeout(timeout);
                                    reject(new Error("AliyunCaptcha SDK not loaded"));
                                    return;
                                }

                                var config = getSceneConfig();

                                // 阿里云验证码SDK的语言映射
                                var aliyunLanguageMapping = {
                                    cn: "cn",
                                    en: "en",
                                    es: "es",
                                    ptbr: "pt",
                                    ru: "ru",
                                };

                                var useLang = aliyunLanguageMapping[currentLanguage] || "cn";

                                console.log("Initializing captcha with config:", config, "language:", useLang);

                                window.initAliyunCaptcha({
                                    SceneId: config.SceneId,
                                    prefix: config.prefix,
                                    mode: "popup",
                                    element: "#captcha-element",
                                    button: "#button",
                                    success: handleCaptchaSuccess,
                                    fail: handleCaptchaFail,
                                    getInstance: function (instance) {
                                        clearTimeout(timeout);
                                        captcha = instance;
                                        console.log("Captcha instance initialized successfully");
                                        if (initResolve) {
                                            initResolve();
                                            initResolve = null;
                                            initReject = null;
                                        }
                                    },
                                    slideStyle: {
                                        width: 360,
                                        height: 40,
                                    },
                                    language: useLang,
                                    onClose: handleCaptchaClose,
                                    onError: function (error) {
                                        clearTimeout(timeout);
                                        console.error("Captcha initialization error:", error);
                                        handleCaptchaError(error);
                                        if (initReject) {
                                            initReject(error);
                                            initResolve = null;
                                            initReject = null;
                                        }
                                    },
                                });
                            } catch (error) {
                                clearTimeout(timeout);
                                console.error("Error during captcha initialization:", error);
                                reject(error);
                            }
                        })
                        .catch(function (error) {
                            clearTimeout(timeout);
                            console.error("SDK loading failed:", error);
                            reject(error);
                        });
                });
            }

            // 验证成功回调
            function handleCaptchaSuccess(captchaVerifyParam) {
                console.log("🎉 Captcha verification success:", captchaVerifyParam);
                notifyAndroid("onCaptchaSuccess", {
                    captchaVerifyParam: captchaVerifyParam,
                });
            }

            // 验证失败回调
            function handleCaptchaFail(result) {
                console.error("❌ Captcha verification failed:", result);
                // 隐藏全屏Loading，显示验证码容器
                hideFullscreenLoading();
                showStatus(ValidationState.ERROR, "Verification Failed, Please Retry");
                notifyAndroid("onCaptchaFail", {
                    success: false,
                    error: "Captcha verification failed",
                    result: result,
                });
                isValidating = false;
            }

            // 验证关闭回调
            function handleCaptchaClose() {
                console.log("Captcha closed");
                showStatus(ValidationState.ERROR, "Verification Cancelled");
                notifyAndroid("onCaptchaCancelled", {
                    success: false,
                    error: "User cancelled",
                });
                isValidating = false;
            }

            // 验证错误回调
            function handleCaptchaError(error) {
                console.error("Captcha error:", error);
                showStatus(ValidationState.ERROR, "Verification Failed, Please Retry");
                notifyAndroid("onCaptchaError", {
                    success: false,
                    error: "Captcha system error",
                    details: error,
                });
                isValidating = false;
            }
            // 通知安卓端
            function notifyAndroid(method, data) {
                try {
                    // 方式1: 通过JavascriptInterface
                    if (window.AndroidInterface && window.AndroidInterface[method]) {
                        window.AndroidInterface[method](JSON.stringify(data));
                    }

                    // 方式2: 通过统一的回调接口
                    if (window.AndroidInterface && window.AndroidInterface.onCaptchaCallback) {
                        window.AndroidInterface.onCaptchaCallback(method, JSON.stringify(data));
                    }

                    // 方式3: 兼容旧版本接口
                    if (window.testInterface && window.testInterface.getCaptchaVerifyParam) {
                        if (method === "onCaptchaSuccess") {
                            window.testInterface.getCaptchaVerifyParam(data.captchaVerifyParam);
                        }
                    }

                    console.log("Android notification sent:", method, data);
                } catch (error) {
                    console.error("Failed to notify Android:", error);
                }
            }

            // 销毁验证码
            function destroyCaptcha() {
                if (captcha) {
                    try {
                        captcha.destroyCaptcha();
                    } catch (error) {
                        console.error("Error destroying captcha:", error);
                    }
                    captcha = null;
                }
            }

            // 开始验证
            function startValidation() {
                console.log("startValidation called, isValidating:", isValidating);

                if (isValidating) {
                    console.log("Already validating, returning");
                    return;
                }

                // document.querySelector(".captcha-container").style.display = "block";

                isValidating = true;
                hideStatus();
                showStatus(ValidationState.LOADING, "Verifying...");

                if (!captcha) {
                    console.log("Captcha not initialized, initializing...");
                    initCaptcha()
                        .then(function () {
                            console.log("Captcha initialized successfully, triggering validation");
                            // 验证码初始化成功后，直接触发验证弹窗
                            triggerCaptchaValidation();
                        })
                        .catch(function (error) {
                            console.error("Failed to initialize captcha:", error);
                            showStatus(ValidationState.ERROR, "Verification Failed, Please Retry");
                            isValidating = false;
                        });
                } else {
                    console.log("Captcha already initialized, showing captcha");
                    // 如果验证码已经初始化，直接显示
                    triggerCaptchaValidation();
                }
            }

            // 触发验证码验证（优化浏览器兼容性）
            function triggerCaptchaValidation() {
                console.log("🚀 Triggering captcha validation...");

                if (!captcha) {
                    console.error("❌ No captcha instance available");
                    showStatus(ValidationState.ERROR, "Verification Failed, Please Retry");
                    isValidating = false;
                    return;
                }

                const browserInfo = getBrowserInfo();
                console.log("🌐 Browser detected:", browserInfo.browserName);

                // 尝试多种方式触发验证码
                try {
                    console.log("📱 Using button click trigger");
                    var captchaBtn = document.getElementById("button");
                    if (captchaBtn) {
                          captchaBtn.click();
                    } else {
                        console.error("❌ validate-button not found");
                        showStatus(ValidationState.ERROR, "Verification Failed, Please Retry");
                        isValidating = false;
                    }
                } catch (error) {
                    console.error("❌ Error triggering captcha:", error);
                    showStatus(ValidationState.ERROR, "Verification Failed, Please Retry");
                    isValidating = false;
                }
            }

            // 按钮点击处理函数
            function handleButtonClick(e) {
                console.log("Button clicked");
                if (!isValidating) {
                    e.preventDefault();
                    startValidation();
                }
            }

            // 设置语言（主要供安卓端调用）
            function setLanguage(lang) {
                // 语言映射
                var languageMapping = {
                    CN: "cn",
                    EN: "en",
                    ES: "es",
                    PTBR: "ptbr",
                    PT: "ptbr",
                    RU: "ru",
                };

                currentLanguage = languageMapping[lang.toUpperCase()] || lang.toLowerCase() || "cn";
                console.log("语言设置为:", currentLanguage);

                // 如果验证码已初始化，需要重新初始化以应用新语言
                if (captcha) {
                    destroyCaptcha();
                }
            }

            // 设置环境（主要供安卓端调用）
            function setEnvironment(env) {
                isDevelopment = env === "development" || env === "dev" || env === "test";
                console.log("环境设置为:", isDevelopment ? "development" : "production");
            }

            // 测试验证码函数（模仿Vue组件的popup方法）
            function testCaptcha() {
                console.log("🧪 Test captcha triggered");

                // 模仿Vue组件中的popup方法
                if (captcha && document.getElementById("validate-button")) {
                    console.log("🎯 Simulating Vue component popup method");
                    document.getElementById("validate-button").click();
                    return;
                }

                // 输出验证码实例的所有可用方法
                if (captcha) {
                    console.log("Captcha instance methods:", Object.getOwnPropertyNames(captcha));
                    console.log("Captcha instance:", captcha);

                    // 尝试直接调用验证码
                    if (typeof captcha.verify === "function") {
                        console.log("Calling captcha.verify()");
                        captcha.verify();
                    } else if (typeof captcha.showCaptcha === "function") {
                        console.log("Calling captcha.showCaptcha()");
                        captcha.showCaptcha();
                    } else {
                        console.log("No verify or showCaptcha method found, available methods:");
                        for (var prop in captcha) {
                            if (typeof captcha[prop] === "function") {
                                console.log("- " + prop);
                            }
                        }

                        // 尝试其他可能的方法
                        if (typeof captcha.popup === "function") {
                            console.log("Trying captcha.popup()");
                            captcha.popup();
                        }
                    }
                } else {
                    console.log("❌ No captcha instance available");
                    // 尝试重新初始化
                    startValidation();
                }
            }

            // 暴露给安卓端的接口
            window.CaptchaWebView = {
                startValidation: startValidation,
                setLanguage: setLanguage,
                setEnvironment: setEnvironment,
                destroyCaptcha: destroyCaptcha,
                testCaptcha: testCaptcha,
                getStatus: function () {
                    return {
                        isValidating: isValidating,
                        hasCaptcha: !!captcha,
                        currentLanguage: currentLanguage,
                        isDevelopment: isDevelopment,
                    };
                },
            };

            // 暴露测试函数到全局
            window.testCaptcha = testCaptcha;

            // 模仿Tool.loadJS的加载方式
            function loadJS(url) {
                return new Promise(function(resolve, reject) {
                    const d = document;
                    let s = d.createElement('script');
                    s.type = 'text/javascript';
                    s.charset = 'utf-8';

                    if (s.readyState) {
                        // IE
                        s.onreadystatechange = function () {
                            if (s.readyState === 'loaded' || s.readyState === 'complete') {
                                s.onreadystatechange = null;
                                resolve();
                            }
                        };
                    } else {
                        s.onload = function () {
                            resolve();
                        };
                        s.onerror = function(error) {
                            reject(error);
                        };
                    }
                    s.src = url;
                    d.getElementsByTagName('head')[0].appendChild(s);
                });
            }

            // 异步加载vConsole（仅开发环境）
            function loadVConsoleAsync() {
                if (!isDevelopment) {
                    console.log("🔧 Production environment, skipping vConsole");
                    return Promise.resolve();
                }

                return new Promise(function(resolve, reject) {
                    console.log("🔧 Loading vConsole for development...");

                    // 检查是否已经加载
                    if (window.VConsole) {
                        console.log("✅ vConsole already loaded");
                        resolve();
                        return;
                    }

                    loadJS('https://unpkg.com/vconsole@latest/dist/vconsole.min.js')
                        .then(function() {
                            console.log("✅ vConsole script loaded");
                            // 等待vConsole可用
                            setTimeout(function() {
                                if (window.VConsole) {
                                    try {
                                        var vConsole = new VConsole();
                                        console.log("✅ vConsole initialized successfully");
                                        resolve();
                                    } catch (error) {
                                        console.error("❌ Failed to initialize vConsole:", error);
                                        resolve(); // 不阻塞主流程
                                    }
                                } else {
                                    console.error("❌ vConsole not available after loading");
                                    resolve(); // 不阻塞主流程
                                }
                            }, 100);
                        })
                        .catch(function(error) {
                            console.error("❌ Failed to load vConsole:", error);
                            resolve(); // 不阻塞主流程，即使vConsole加载失败也要继续
                        });
                });
            }

            // 确保SDK加载（模仿Vue组件的方式）
            function ensureSDKLoaded(retryCount = 0) {
                return new Promise(function (resolve, reject) {
                    if (window.initAliyunCaptcha) {
                        console.log("✅ AliyunCaptcha SDK already loaded");
                        resolve();
                        return;
                    }

                    const maxRetries = 3;
                    const retryDelay = 2000;

                    console.log(`🔄 Loading AliyunCaptcha SDK (attempt ${retryCount + 1}/${maxRetries})...`);
                    console.log("📥 下载无痕验证，重新下载");

                    loadJS('https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js')
                        .then(function() {
                            console.log("✅ AliyunCaptcha SDK loaded successfully");
                            // 等待SDK完全初始化
                            setTimeout(function () {
                                if (window.initAliyunCaptcha) {
                                    console.log("🎯 SDK initialization completed");
                                    resolve();
                                } else {
                                    console.error("❌ SDK loaded but initAliyunCaptcha not available");
                                    reject(new Error("SDK loaded but initAliyunCaptcha not available"));
                                }
                            }, 300);
                        })
                        .catch(function(error) {
                            console.error("❌ Failed to load AliyunCaptcha SDK:", error);
                            if (retryCount < maxRetries - 1) {
                                console.log(`🔄 Retrying SDK load in ${retryDelay}ms...`);
                                setTimeout(function() {
                                    ensureSDKLoaded(retryCount + 1).then(resolve).catch(reject);
                                }, retryDelay);
                            } else {
                                reject(new Error("Failed to load SDK after " + maxRetries + " attempts"));
                            }
                        });
                });
            }

            // 浏览器类型检测
            function getBrowserInfo() {
                const ua = navigator.userAgent;
                const isAndroid = /Android/i.test(ua);
                const isWeChat = /MicroMessenger/i.test(ua);
                const isAndroidBrowser = isAndroid && /Android.*Browser/i.test(ua);
                const isChrome = /Chrome/i.test(ua) && !/Edge/i.test(ua);
                const isSafari = /Safari/i.test(ua) && !/Chrome/i.test(ua);

                return {
                    isAndroid,
                    isWeChat,
                    isAndroidBrowser,
                    isChrome,
                    isSafari,
                    browserName: isWeChat ? 'WeChat' :
                                isAndroidBrowser ? 'AndroidBrowser' :
                                isChrome ? 'Chrome' :
                                isSafari ? 'Safari' : 'Unknown'
                };
            }

            // 设备信息检测
            function logDeviceInfo() {
                const browserInfo = getBrowserInfo();
                console.log("📱 Device Info:", {
                    "User Agent": navigator.userAgent,
                    "Platform": navigator.platform,
                    "Language": navigator.language,
                    "Cookie Enabled": navigator.cookieEnabled,
                    "Online": navigator.onLine,
                    "Screen Size": screen.width + "x" + screen.height,
                    "Viewport Size": window.innerWidth + "x" + window.innerHeight,
                    "Device Pixel Ratio": window.devicePixelRatio || 1,
                    "Touch Points": navigator.maxTouchPoints || 0,
                    "Is Mobile": /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
                    "Browser": browserInfo.browserName,
                    "Is WeChat": browserInfo.isWeChat,
                    "Is Android Browser": browserInfo.isAndroidBrowser
                });
            }

            // 网络状态检测
            function checkNetworkStatus() {
                if (navigator.onLine) {
                    console.log("📡 Network: Online");
                    return true;
                } else {
                    console.error("📵 Network: Offline - Please check your internet connection");
                    showStatus(ValidationState.ERROR, "网络连接失败，请检查网络设置");
                    return false;
                }
            }

            // 监听网络状态变化
            window.addEventListener('online', function() {
                console.log("🌐 Network restored");
                updateDebugInfo();
            });

            window.addEventListener('offline', function() {
                console.error("🌐 Network lost");
                showStatus(ValidationState.ERROR, "网络连接已断开");
                updateDebugInfo();
            });

            // 重写console.clear，防止清空日志
            (function() {
                var originalConsoleClear = console.clear;
                console.clear = function() {
                    console.log("⚠️ console.clear() 被阻止 - 保留调试日志");
                    // 如果需要真的清空，可以注释掉这行
                    // originalConsoleClear.apply(console, arguments);
                };

                // 暴露恢复方法（仅开发环境）
                window.restoreConsoleClear = function() {
                    console.clear = originalConsoleClear;
                    console.log("✅ console.clear() 已恢复原始功能");
                };
            })();

            // 页面加载完成后初始化
            document.addEventListener("DOMContentLoaded", function () {
                console.log("DOM loaded, initializing...");

                // 显示Loading状态
                showFullscreenLoading();

                logDeviceInfo(); // 记录设备信息

                // 从地址栏获取配置
                var urlConfig = getUrlConfig();

                // 应用配置
                currentLanguage = urlConfig.language;
                isDevelopment = urlConfig.isDevelopment;

                // 异步加载vConsole（仅开发环境，不阻塞主流程）
                loadVConsoleAsync();

                console.log("最终配置:", {
                    语言: currentLanguage,
                    环境: isDevelopment ? "development" : "production",
                });

                // 绑定按钮事件（支持触摸设备，优化浏览器兼容性）
                var validateButton = document.getElementById("validate-button");
                if (validateButton) {
                    const browserInfo = getBrowserInfo();

                    // 根据浏览器类型决定事件绑定方式
                    if (browserInfo.isAndroidBrowser) {
                        // Android自带浏览器：只绑定click事件，避免冲突
                        validateButton.addEventListener("click", handleButtonClick);
                        console.log("Button event listener attached (click only for Android Browser)");
                    } else {
                        // 其他浏览器：同时绑定点击和触摸事件
                        validateButton.addEventListener("click", handleButtonClick);
                        validateButton.addEventListener("touchstart", handleButtonClick, { passive: true });
                        console.log("Button event listeners attached (click + touch)");
                    }
                } else {
                    console.error("validate-button not found");
                }

                // 确保SDK加载完成
                showFullscreenLoading();
                ensureSDKLoaded()
                    .then(function () {
                        console.log("SDK ready, captcha can be initialized");
                        showFullscreenLoading();
                        // 延迟一小段时间让用户看到"准备验证"的状态
                        setTimeout(function() {
                            startValidation();
                            updateDebugInfo();
                        }, 500);
                    })
                    .catch(function (error) {
                        console.error("SDK loading failed:", error);
                        hideFullscreenLoading();
                        document.querySelector(".captcha-container").style.display = "block";
                        showStatus(ValidationState.ERROR, "验证码组件加载失败");
                        updateDebugInfo();
                    });

                // 定期更新调试信息
                setInterval(updateDebugInfo, 2000);

                console.log("Captcha WebView initialized");
            });

            // 页面卸载时清理
            window.addEventListener("beforeunload", function () {
                destroyCaptcha();
            });
        </script>
    </body>
</html>

